# Deployment Guide

This guide provides detailed instructions for deploying the Telegram Customer Support Bot in various environments.

## Pre-Deployment Checklist

- [ ] Telegram Bot Token obtained from @BotFather
- [ ] Google Gemini API Key configured
- [ ] Environment variables configured
- [ ] Dependencies installed
- [ ] Bot tested locally
- [ ] Monitoring and logging configured

## Production Deployment

### 1. Docker Deployment (Recommended)

#### Prerequisites
- Docker and Docker Compose installed
- Server with adequate resources (minimum 1GB RAM)

#### Steps

1. **Prepare the environment:**
```bash
# Create deployment directory
mkdir -p /opt/telegram-bot
cd /opt/telegram-bot

# Copy project files
# (Upload your project files here)

# Create data and logs directories
mkdir -p data logs
```

2. **Configure environment:**
```bash
# Copy and edit environment file
cp .env.example .env
nano .env  # Edit with your API keys
```

3. **Deploy with Docker Compose:**
```bash
# Build and start the bot
docker-compose up -d

# Check status
docker-compose ps
docker-compose logs -f telegram-bot
```

4. **Set up log rotation:**
```bash
# Create logrotate configuration
sudo tee /etc/logrotate.d/telegram-bot << EOF
/opt/telegram-bot/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    postrotate
        docker-compose -f /opt/telegram-bot/docker-compose.yml restart telegram-bot
    endscript
}
EOF
```

### 2. Systemd Service Deployment

#### Prerequisites
- Linux server with systemd
- Python 3.11+ installed

#### Steps

1. **Create service user:**
```bash
sudo useradd -r -s /bin/false telegram-bot
```

2. **Install the application:**
```bash
# Create application directory
sudo mkdir -p /opt/telegram-bot
sudo cp -r . /opt/telegram-bot/
sudo chown -R telegram-bot:telegram-bot /opt/telegram-bot

# Create virtual environment
cd /opt/telegram-bot
sudo -u telegram-bot python -m venv venv
sudo -u telegram-bot venv/bin/pip install -r requirements.txt
```

3. **Configure environment:**
```bash
sudo -u telegram-bot cp .env.example .env
sudo -u telegram-bot nano .env  # Edit with your API keys
```

4. **Install and start service:**
```bash
# Install systemd service
sudo cp systemd/telegram-bot.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable telegram-bot
sudo systemctl start telegram-bot

# Check status
sudo systemctl status telegram-bot
sudo journalctl -u telegram-bot -f
```

### 3. Cloud Platform Deployment

#### Heroku

1. **Prepare for Heroku:**
```bash
# Create Procfile
echo "worker: python run_bot.py" > Procfile

# Create runtime.txt
echo "python-3.11.0" > runtime.txt
```

2. **Deploy to Heroku:**
```bash
# Install Heroku CLI and login
heroku create your-bot-name
heroku config:set TELEGRAM_BOT_TOKEN=your_token
heroku config:set GEMINI_API_KEY=your_key
git push heroku main
heroku ps:scale worker=1
```

#### AWS EC2

1. **Launch EC2 instance:**
   - Choose Ubuntu 22.04 LTS
   - Minimum t3.micro (1GB RAM)
   - Configure security group for SSH access

2. **Install dependencies:**
```bash
sudo apt update
sudo apt install -y python3.11 python3.11-venv python3-pip docker.io docker-compose
sudo systemctl enable docker
sudo systemctl start docker
```

3. **Deploy using Docker method above**

#### Google Cloud Platform

1. **Create Compute Engine instance:**
```bash
gcloud compute instances create telegram-bot \
    --image-family=ubuntu-2204-lts \
    --image-project=ubuntu-os-cloud \
    --machine-type=e2-micro \
    --zone=us-central1-a
```

2. **SSH and deploy using Docker method**

## Environment-Specific Configurations

### Development Environment

```env
LOG_LEVEL=DEBUG
MAX_REQUESTS_PER_MINUTE=10
RESPONSE_DELAY_SECONDS=0.5
```

### Staging Environment

```env
LOG_LEVEL=INFO
MAX_REQUESTS_PER_MINUTE=20
RESPONSE_DELAY_SECONDS=1
BOT_NAME=Customer Support Bot (Staging)
```

### Production Environment

```env
LOG_LEVEL=INFO
MAX_REQUESTS_PER_MINUTE=30
RESPONSE_DELAY_SECONDS=1
MAX_CONVERSATION_HISTORY=100
```

## Monitoring and Maintenance

### Health Checks

1. **Create health check script:**
```bash
#!/bin/bash
# health_check.sh

BOT_PID=$(pgrep -f "python run_bot.py")
if [ -z "$BOT_PID" ]; then
    echo "Bot is not running"
    exit 1
else
    echo "Bot is running (PID: $BOT_PID)"
    exit 0
fi
```

2. **Set up monitoring with cron:**
```bash
# Add to crontab
*/5 * * * * /opt/telegram-bot/health_check.sh || systemctl restart telegram-bot
```

### Log Management

1. **Configure log rotation:**
```bash
# /etc/logrotate.d/telegram-bot
/opt/telegram-bot/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 telegram-bot telegram-bot
    postrotate
        systemctl reload telegram-bot
    endscript
}
```

### Backup Strategy

1. **Backup conversation data:**
```bash
#!/bin/bash
# backup_conversations.sh

BACKUP_DIR="/opt/backups/telegram-bot"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
cp /opt/telegram-bot/data/conversations.json $BACKUP_DIR/conversations_$DATE.json

# Keep only last 30 days of backups
find $BACKUP_DIR -name "conversations_*.json" -mtime +30 -delete
```

2. **Schedule backups:**
```bash
# Add to crontab
0 2 * * * /opt/telegram-bot/backup_conversations.sh
```

## Security Hardening

### File Permissions

```bash
# Set secure permissions
sudo chmod 600 /opt/telegram-bot/.env
sudo chmod 755 /opt/telegram-bot/run_bot.py
sudo chown -R telegram-bot:telegram-bot /opt/telegram-bot
```

### Firewall Configuration

```bash
# UFW configuration
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw enable
```

### SSL/TLS (if using webhooks)

```bash
# Generate SSL certificate with Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

## Scaling Considerations

### Horizontal Scaling

For high-volume deployments:

1. **Load balancer configuration**
2. **Multiple bot instances**
3. **Shared conversation storage (Redis/Database)**
4. **Message queue for processing**

### Vertical Scaling

Resource requirements by message volume:

- **Low volume** (< 100 messages/day): 1GB RAM, 1 CPU
- **Medium volume** (< 1000 messages/day): 2GB RAM, 2 CPU
- **High volume** (> 1000 messages/day): 4GB RAM, 4 CPU

## Troubleshooting Deployment Issues

### Common Problems

1. **Bot not starting:**
   - Check API keys
   - Verify Python version
   - Check file permissions

2. **Memory issues:**
   - Monitor conversation file size
   - Implement cleanup routines
   - Increase server resources

3. **Network connectivity:**
   - Check firewall rules
   - Verify DNS resolution
   - Test API endpoints

### Debug Commands

```bash
# Check bot status
systemctl status telegram-bot

# View logs
journalctl -u telegram-bot -f

# Test configuration
python -c "from config import Config; Config.validate()"

# Check network connectivity
curl -s https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/getMe
```

## Rollback Procedures

### Quick Rollback

```bash
# Stop current version
docker-compose down

# Restore previous version
git checkout previous-tag
docker-compose up -d
```

### Data Recovery

```bash
# Restore conversation backup
cp /opt/backups/telegram-bot/conversations_YYYYMMDD_HHMMSS.json /opt/telegram-bot/data/conversations.json
systemctl restart telegram-bot
```

---

For additional support, refer to the main README.md or contact the development team.
