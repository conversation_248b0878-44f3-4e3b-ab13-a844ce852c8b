FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create directories for data and logs
RUN mkdir -p /app/data /app/logs

# Make run script executable
RUN chmod +x run_bot.py

# Set environment variables
ENV PYTHONPATH=/app
ENV CONVERSATION_MEMORY_FILE=/app/data/conversations.json

# Run the bot
CMD ["python", "run_bot.py"]
