# Telegram Customer Support Bot

A sophisticated Python-based Telegram bot designed for automated customer support with AI-powered responses, conversation memory, and intelligent escalation to human support.

## Features

- 🤖 **AI-Powered Responses**: Uses Google Gemini API for intelligent, context-aware responses
- 💬 **Conversation Memory**: Tracks user interactions and maintains context across conversations
- 🎯 **Intent Recognition**: Analyzes user messages to understand intent and sentiment
- 🚀 **Smart Escalation**: Automatically escalates complex issues to human support
- 📊 **Professional Communication**: Maintains courteous, professional tone in all interactions
- 🔄 **Rate Limiting**: Built-in throttling to handle high message volumes
- 🛡️ **Error Handling**: Comprehensive error handling and graceful degradation
- 📝 **Logging**: Detailed logging for monitoring and debugging
- 🐳 **Docker Support**: Ready for containerized deployment

## Quick Start

### Prerequisites

- Python 3.11+
- Telegram <PERSON><PERSON>ken (from @BotFather)
- Google Gemini API Key

### Installation

1. **Clone and setup:**
```bash
git clone <your-repo>
cd chatbot
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. **Run the bot:**
```bash
python run_bot.py
```

## Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Required
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
GEMINI_API_KEY=your_gemini_api_key

# Optional
BOT_NAME=Customer Support Bot
SUPPORT_CONTACT=@your_support_username
LOG_LEVEL=INFO
MAX_CONVERSATION_HISTORY=50
MAX_REQUESTS_PER_MINUTE=30
RESPONSE_DELAY_SECONDS=1
```

### Getting API Keys

#### Telegram Bot Token
1. Message @BotFather on Telegram
2. Send `/newbot` and follow instructions
3. Copy the bot token

#### Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key

## Deployment Options

### Option 1: Docker (Recommended)

```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f telegram-bot
```

### Option 2: Systemd Service

```bash
# Copy files to deployment directory
sudo mkdir -p /opt/telegram-bot
sudo cp -r . /opt/telegram-bot/
sudo chown -R telegram-bot:telegram-bot /opt/telegram-bot

# Install systemd service
sudo cp systemd/telegram-bot.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable telegram-bot
sudo systemctl start telegram-bot
```

### Option 3: Direct Python

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Run bot
python run_bot.py
```

## Bot Commands

- `/start` - Initialize conversation with the bot
- `/help` - Show help message and bot capabilities
- `/status` - Display bot status and user conversation stats

## Architecture

### Core Components

1. **telegram_bot.py** - Main bot implementation with message handling
2. **ai_processor.py** - AI-powered response generation using Gemini
3. **conversation_memory.py** - JSON-based conversation tracking
4. **config.py** - Configuration management and validation

### Message Flow

1. User sends message → Bot receives via Telegram API
2. Message stored in conversation memory
3. AI analyzes intent and generates response
4. Bot checks if escalation to human support is needed
5. Response sent to user and stored in memory

### Conversation Memory

The bot maintains conversation history in JSON format:
- User messages and bot responses
- Timestamps and metadata
- User information and context
- Conversation summaries for AI context

## Monitoring and Logs

### Log Files
- `bot.log` - Main application logs
- Console output for real-time monitoring

### Log Levels
- `DEBUG` - Detailed debugging information
- `INFO` - General operational messages
- `WARNING` - Warning conditions
- `ERROR` - Error conditions

### Monitoring Commands

```bash
# View real-time logs
tail -f bot.log

# Check bot status (if using systemd)
sudo systemctl status telegram-bot

# View Docker logs
docker-compose logs -f telegram-bot
```

## Customization

### Modifying AI Responses

Edit `ai_processor.py` to customize:
- Response generation prompts
- Intent analysis logic
- Escalation criteria
- Fallback responses

### Adding New Commands

Add handlers in `telegram_bot.py`:

```python
self.application.add_handler(CommandHandler("newcommand", self.new_command_handler))
```

### Conversation Memory

Customize memory behavior in `conversation_memory.py`:
- Memory retention periods
- Context tracking
- User information storage

## Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check API keys in `.env`
   - Verify bot token with @BotFather
   - Check logs for errors

2. **AI responses failing**
   - Verify Gemini API key
   - Check API quota limits
   - Review error logs

3. **Memory issues**
   - Check file permissions for `conversations.json`
   - Verify disk space
   - Review memory cleanup settings

### Debug Mode

Enable debug logging:
```env
LOG_LEVEL=DEBUG
```

### Health Checks

The bot includes built-in health monitoring:
- Automatic error recovery
- Connection retry logic
- Graceful shutdown handling

## Security Considerations

- Store API keys securely (use environment variables)
- Implement rate limiting (built-in)
- Monitor for abuse patterns
- Regular security updates
- Secure file permissions for conversation data

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Check the troubleshooting section
- Review logs for error details
- Open an issue on GitHub
- Contact the development team

---

**Note**: This bot is designed for customer support scenarios. Ensure compliance with your organization's data privacy and security policies before deployment.
