"""
AI-powered message processing using Google Gemini API.
"""
import google.generativeai as genai
import asyncio
import logging
from typing import Dict, List, Optional
from datetime import datetime
from config import Config

logger = logging.getLogger(__name__)

class AIProcessor:
    """Handles AI-powered response generation and intent recognition."""
    
    def __init__(self):
        self.model = None
        self.setup_gemini()
        
    def setup_gemini(self):
        """Initialize Gemini AI model."""
        try:
            genai.configure(api_key=Config.GEMINI_API_KEY)
            self.model = genai.GenerativeModel('gemini-pro')
            logger.info("Gemini AI model initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Gemini AI: {e}")
            raise
    
    async def generate_response(self, user_message: str, conversation_context: str = "", user_info: Dict = None) -> str:
        """Generate an AI response to user message."""
        try:
            # Build the prompt with context
            prompt = self._build_prompt(user_message, conversation_context, user_info)
            
            # Generate response using Gemini
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            
            if response and response.text:
                return response.text.strip()
            else:
                return self._get_fallback_response()
                
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return self._get_fallback_response()
    
    def _build_prompt(self, user_message: str, conversation_context: str = "", user_info: Dict = None) -> str:
        """Build a comprehensive prompt for the AI model."""
        
        system_prompt = f"""You are {Config.BOT_NAME}, a professional customer support assistant for a Telegram channel. Your role is to provide helpful, accurate, and courteous responses to user inquiries.

GUIDELINES:
1. Be professional, friendly, and helpful
2. Provide clear, actionable solutions when possible
3. Ask clarifying questions if the user's intent is unclear
4. Escalate to human support when necessary by mentioning {Config.SUPPORT_CONTACT}
5. Keep responses concise but comprehensive
6. Use a conversational tone while maintaining professionalism
7. If you don't know something, admit it and offer to connect them with human support

ESCALATION SCENARIOS:
- Complex technical issues requiring specialized knowledge
- Account-specific problems requiring access to user data
- Billing or payment issues
- Complaints or disputes
- Requests for refunds or account changes

CONVERSATION CONTEXT:
{conversation_context if conversation_context else "This is a new conversation."}

USER MESSAGE: {user_message}

Please provide a helpful response:"""

        return system_prompt
    
    async def analyze_intent(self, user_message: str) -> Dict[str, any]:
        """Analyze user message to determine intent and extract key information."""
        try:
            intent_prompt = f"""Analyze the following user message and determine:
1. Primary intent (greeting, question, complaint, request, technical_issue, billing, other)
2. Urgency level (low, medium, high)
3. Requires human support (yes/no)
4. Key topics mentioned
5. Sentiment (positive, neutral, negative)

User message: "{user_message}"

Respond in this exact format:
Intent: [intent]
Urgency: [urgency]
Human Support: [yes/no]
Topics: [comma-separated topics]
Sentiment: [sentiment]"""

            response = await asyncio.to_thread(self.model.generate_content, intent_prompt)
            
            if response and response.text:
                return self._parse_intent_response(response.text)
            else:
                return self._get_default_intent()
                
        except Exception as e:
            logger.error(f"Error analyzing intent: {e}")
            return self._get_default_intent()
    
    def _parse_intent_response(self, response_text: str) -> Dict[str, any]:
        """Parse the intent analysis response."""
        try:
            lines = response_text.strip().split('\n')
            intent_data = {}
            
            for line in lines:
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip().lower().replace(' ', '_')
                    value = value.strip()
                    
                    if key == 'intent':
                        intent_data['intent'] = value.lower()
                    elif key == 'urgency':
                        intent_data['urgency'] = value.lower()
                    elif key == 'human_support':
                        intent_data['requires_human'] = value.lower() == 'yes'
                    elif key == 'topics':
                        intent_data['topics'] = [topic.strip() for topic in value.split(',')]
                    elif key == 'sentiment':
                        intent_data['sentiment'] = value.lower()
            
            return intent_data
            
        except Exception as e:
            logger.error(f"Error parsing intent response: {e}")
            return self._get_default_intent()
    
    def _get_default_intent(self) -> Dict[str, any]:
        """Return default intent analysis."""
        return {
            'intent': 'question',
            'urgency': 'medium',
            'requires_human': False,
            'topics': ['general'],
            'sentiment': 'neutral'
        }
    
    def _get_fallback_response(self) -> str:
        """Return a fallback response when AI fails."""
        return f"""Thank you for your message! I'm here to help you with your inquiry.

I'm currently experiencing some technical difficulties, but I'd be happy to assist you. Could you please provide more details about what you need help with?

If you need immediate assistance, please contact our human support team at {Config.SUPPORT_CONTACT}.

I apologize for any inconvenience and appreciate your patience! 🙏"""

    async def generate_greeting_response(self, user_name: str = None) -> str:
        """Generate a personalized greeting response."""
        name_part = f" {user_name}" if user_name else ""
        
        greetings = [
            f"Hello{name_part}! 👋 Welcome to our support channel. How can I assist you today?",
            f"Hi{name_part}! I'm here to help with any questions or issues you might have. What can I do for you?",
            f"Welcome{name_part}! 🌟 I'm your customer support assistant. Feel free to ask me anything!"
        ]
        
        # Use AI to generate a more personalized greeting if possible
        try:
            prompt = f"Generate a friendly, professional greeting for a customer support bot. The user's name is {user_name if user_name else 'not provided'}. Keep it brief and welcoming."
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            
            if response and response.text:
                return response.text.strip()
        except Exception as e:
            logger.error(f"Error generating AI greeting: {e}")
        
        # Fallback to predefined greetings
        import random
        return random.choice(greetings)
    
    async def should_escalate_to_human(self, intent_data: Dict, user_message: str) -> bool:
        """Determine if the conversation should be escalated to human support."""
        
        # Check intent analysis
        if intent_data.get('requires_human', False):
            return True
        
        # Check urgency and sentiment
        if intent_data.get('urgency') == 'high' and intent_data.get('sentiment') == 'negative':
            return True
        
        # Check for specific keywords that typically require human intervention
        escalation_keywords = [
            'refund', 'cancel', 'billing', 'payment', 'charge', 'account',
            'angry', 'frustrated', 'complaint', 'manager', 'supervisor',
            'legal', 'lawsuit', 'dispute', 'unauthorized'
        ]
        
        message_lower = user_message.lower()
        if any(keyword in message_lower for keyword in escalation_keywords):
            return True
        
        return False
