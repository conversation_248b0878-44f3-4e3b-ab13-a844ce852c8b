#!/usr/bin/env python3
"""
Debug script to identify specific bot issues.
"""
import asyncio
import os
import sys

async def test_gemini_api():
    """Test Gemini API connectivity."""
    print("Testing Gemini API...")
    try:
        from ai_processor import AIProcessor
        ai = AIProcessor()
        
        # Test basic response generation
        response = await ai.generate_response("Hello, this is a test message", [])
        print(f"✓ Gemini API working: {response[:50]}...")
        return True
    except Exception as e:
        print(f"✗ Gemini API failed: {e}")
        return False

async def test_telegram_token():
    """Test Telegram bot token."""
    print("Testing Telegram token...")
    try:
        from telegram import Bot
        from config import Config
        
        bot = Bot(token=Config.TELEGRAM_BOT_TOKEN)
        me = await bot.get_me()
        print(f"✓ Telegram token valid: @{me.username}")
        return True
    except Exception as e:
        print(f"✗ Telegram token failed: {e}")
        return False

async def test_bot_components():
    """Test individual bot components."""
    print("Testing bot components...")
    try:
        from telegram_bot import CustomerSupportBot
        from config import Config
        
        Config.validate()
        bot = CustomerSupportBot()
        await bot.initialize()
        
        print("✓ Bot components initialized")
        
        # Test handlers
        handlers = bot.application.handlers
        print(f"✓ {len(handlers)} handler groups registered")
        
        return True
    except Exception as e:
        print(f"✗ Bot components failed: {e}")
        return False

async def test_conversation_memory():
    """Test conversation memory system."""
    print("Testing conversation memory...")
    try:
        from conversation_memory import ConversationMemory
        
        memory = ConversationMemory()
        await memory.load_conversations()
        
        # Test adding a conversation
        await memory.add_message("test_user", "user", "Hello")
        await memory.add_message("test_user", "assistant", "Hi there!")
        
        history = await memory.get_conversation_history("test_user")
        print(f"✓ Conversation memory working: {len(history)} messages")
        
        return True
    except Exception as e:
        print(f"✗ Conversation memory failed: {e}")
        return False

def test_imports():
    """Test all required imports."""
    print("Testing imports...")
    try:
        import telegram
        import google.generativeai as genai
        from telegram.ext import Application, CommandHandler, MessageHandler, filters
        print("✓ All imports successful")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

async def main():
    """Run all diagnostic tests."""
    print("=" * 60)
    print("Bot Diagnostic Test - Identifying Issues")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Gemini API", test_gemini_api),
        ("Telegram Token", test_telegram_token),
        ("Bot Components", test_bot_components),
        ("Conversation Memory", test_conversation_memory),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Bot should be working.")
        print("If you're still having issues, please describe the specific problem.")
    else:
        print(f"\n❌ {len(results) - passed} test(s) failed. These need to be fixed.")

if __name__ == "__main__":
    asyncio.run(main())
