version: '3.8'

services:
  telegram-bot:
    build: .
    container_name: customer-support-bot
    restart: unless-stopped
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - BOT_NAME=${BOT_NAME:-Customer Support Bot}
      - SUPPORT_CONTACT=${SUPPORT_CONTACT:-@support}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - MAX_CONVERSATION_HISTORY=${MAX_CONVERSATION_HISTORY:-50}
      - MAX_REQUESTS_PER_MINUTE=${MAX_REQUESTS_PER_MINUTE:-30}
      - RESPONSE_DELAY_SECONDS=${RESPONSE_DELAY_SECONDS:-1}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    working_dir: /app
    command: python run_bot.py
