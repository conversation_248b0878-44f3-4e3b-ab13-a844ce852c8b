#!/usr/bin/env python3
"""
Final test to confirm the bot is working properly.
"""
import asyncio
import os
import signal
import time

# Set environment variables
os.environ['TELEGRAM_BOT_TOKEN'] = '**********************************************'
os.environ['GEMINI_API_KEY'] = 'test_key'  # Will fail gracefully without real key

async def test_bot_startup():
    """Test that the bot can start up without errors."""
    print("Testing bot startup...")
    
    try:
        from telegram_bot import CustomerSupportBot
        from config import Config
        
        # Validate config
        Config.validate()
        print("✓ Configuration validated")
        
        # Create bot instance
        bot = CustomerSupportBot()
        print("✓ Bot instance created")
        
        # Initialize bot
        await bot.initialize()
        print("✓ Bot initialized successfully")
        
        # Test that application is created
        if bot.application:
            print("✓ Telegram Application created")
        else:
            print("✗ Telegram Application not created")
            return False
        
        # Test handlers are added
        handlers = bot.application.handlers
        if handlers:
            print(f"✓ {len(handlers)} handler groups added")
        else:
            print("✗ No handlers added")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Bot startup failed: {e}")
        return False

def test_bot_run_briefly():
    """Test that the bot can run briefly without crashing."""
    print("\nTesting bot run (5 second test)...")
    
    import subprocess
    import sys
    
    try:
        # Start the bot process
        process = subprocess.Popen(
            [sys.executable, 'run_bot.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for 5 seconds
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is None:
            print("✓ Bot is running successfully")
            
            # Terminate the process
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            
            return True
        else:
            # Process has already terminated
            stdout, stderr = process.communicate()
            print(f"✗ Bot process terminated unexpectedly")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing bot run: {e}")
        return False

async def main():
    """Main test function."""
    print("=" * 60)
    print("Final Bot Test - Confirming Everything Works")
    print("=" * 60)
    
    # Test startup
    startup_success = await test_bot_startup()
    
    # Test run
    run_success = test_bot_run_briefly()
    
    print("\n" + "=" * 60)
    
    if startup_success and run_success:
        print("🎉 ALL TESTS PASSED!")
        print("\nYour Telegram bot is ready to use!")
        print("\nNext steps:")
        print("1. Get a real Gemini API key from: https://makersuite.google.com/app/apikey")
        print("2. Replace 'your_gemini_api_key_here' in .env file")
        print("3. Run: python3 run_bot.py")
        print("4. Message your bot on Telegram!")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
