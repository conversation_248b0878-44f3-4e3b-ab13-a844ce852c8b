#!/usr/bin/env python3
"""
Entry point for running the Telegram Customer Support Bot.
"""
import asyncio
import signal
import sys
import logging
from telegram_bot import main

logger = logging.getLogger(__name__)

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)

if __name__ == "__main__":
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("<PERSON><PERSON> stopped by user")
    except Exception as e:
        logger.error(f"<PERSON><PERSON> crashed: {e}")
        sys.exit(1)
