#!/usr/bin/env python3
"""
Helper script to set up and test your Gemini API key.
"""
import os
import sys
import google.generativeai as genai
from dotenv import load_dotenv

def test_api_key(api_key):
    """Test if the provided API key works."""
    try:
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-pro')
        response = model.generate_content("Say hello in one word")
        return True, response.text.strip()
    except Exception as e:
        return False, str(e)

def update_env_file(api_key):
    """Update the .env file with the new API key."""
    try:
        # Read current .env file
        with open('.env', 'r') as f:
            content = f.read()
        
        # Replace the API key line
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('GEMINI_API_KEY='):
                lines[i] = f'GEMINI_API_KEY={api_key}'
                break
        
        # Write back to file
        with open('.env', 'w') as f:
            f.write('\n'.join(lines))
        
        return True
    except Exception as e:
        print(f"Error updating .env file: {e}")
        return False

def main():
    """Main setup function."""
    print("🔧 Gemini API Key Setup Helper")
    print("=" * 40)
    
    # Check current status
    load_dotenv()
    current_key = os.getenv('GEMINI_API_KEY')
    
    if current_key and current_key != 'your_actual_gemini_api_key_here':
        print(f"Current API key: {current_key[:10]}...{current_key[-5:]}")
        test_current = input("Test current API key? (y/n): ").lower().strip()
        
        if test_current == 'y':
            print("Testing current API key...")
            success, result = test_api_key(current_key)
            if success:
                print(f"✅ Current API key works! Test response: {result}")
                return
            else:
                print(f"❌ Current API key failed: {result}")
    
    # Get new API key
    print("\n📝 To get a Gemini API key:")
    print("1. Go to: https://makersuite.google.com/app/apikey")
    print("2. Sign in with your Google account")
    print("3. Click 'Create API Key'")
    print("4. Copy the generated key (starts with 'AIzaSy')")
    
    api_key = input("\nEnter your Gemini API key: ").strip()
    
    if not api_key:
        print("❌ No API key provided. Exiting.")
        return
    
    if not api_key.startswith('AIzaSy'):
        print("⚠️  Warning: API key doesn't start with 'AIzaSy'. This might not be a valid Gemini API key.")
        continue_anyway = input("Continue anyway? (y/n): ").lower().strip()
        if continue_anyway != 'y':
            return
    
    # Test the API key
    print("\n🧪 Testing API key...")
    success, result = test_api_key(api_key)
    
    if not success:
        print(f"❌ API key test failed: {result}")
        print("\nPlease check:")
        print("- The API key is correct")
        print("- The API key has the necessary permissions")
        print("- Your internet connection is working")
        return
    
    print(f"✅ API key works! Test response: {result}")
    
    # Update .env file
    print("\n💾 Updating .env file...")
    if update_env_file(api_key):
        print("✅ .env file updated successfully!")
        
        # Test with the bot's configuration
        print("\n🤖 Testing with bot configuration...")
        try:
            from config import Config
            # Reload environment
            load_dotenv(override=True)
            
            if Config.GEMINI_API_KEY == api_key:
                print("✅ Bot configuration updated successfully!")
                print("\n🎉 Setup complete! You can now run your bot with:")
                print("   python3 run_bot.py")
            else:
                print("⚠️  Bot configuration might need a restart to pick up the new key.")
        except Exception as e:
            print(f"⚠️  Could not test bot configuration: {e}")
    else:
        print("❌ Failed to update .env file")

if __name__ == "__main__":
    main()
