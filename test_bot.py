#!/usr/bin/env python3
"""
Test script for the Telegram Customer Support Bot.
"""
import asyncio
import json
import os
import tempfile
import unittest
from unittest.mock import Mock, patch, AsyncMock
import logging

# Set up test environment
os.environ['TELEGRAM_BOT_TOKEN'] = 'test_token'
os.environ['GEMINI_API_KEY'] = 'test_gemini_key'
os.environ['LOG_LEVEL'] = 'ERROR'  # Reduce noise during tests

from conversation_memory import ConversationMemory
from ai_processor import AIProcessor
from config import Config

class TestConversationMemory(unittest.TestCase):
    """Test conversation memory functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        self.temp_file.close()
        self.memory = ConversationMemory(self.temp_file.name)
    
    def tearDown(self):
        """Clean up test environment."""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    async def test_add_and_get_message(self):
        """Test adding and retrieving messages."""
        user_id = "123456"
        message = "Hello, I need help!"
        
        await self.memory.add_message(user_id, message, is_user=True)
        history = await self.memory.get_conversation_history(user_id)
        
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]['message'], message)
        self.assertTrue(history[0]['is_user'])
    
    async def test_conversation_persistence(self):
        """Test that conversations persist across instances."""
        user_id = "123456"
        message = "Test message"
        
        # Add message and save
        await self.memory.add_message(user_id, message, is_user=True)
        await self.memory.save_conversations()
        
        # Create new memory instance and load
        new_memory = ConversationMemory(self.temp_file.name)
        await new_memory.load_conversations()
        
        history = await new_memory.get_conversation_history(user_id)
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]['message'], message)
    
    async def test_conversation_limit(self):
        """Test conversation history limit."""
        user_id = "123456"
        
        # Add more messages than the limit
        for i in range(Config.MAX_CONVERSATION_HISTORY + 10):
            await self.memory.add_message(user_id, f"Message {i}", is_user=True)
        
        history = await self.memory.get_conversation_history(user_id)
        self.assertEqual(len(history), Config.MAX_CONVERSATION_HISTORY)
    
    async def test_user_context(self):
        """Test user context functionality."""
        user_id = "123456"
        
        await self.memory.add_message(user_id, "Hello", is_user=True)
        context = await self.memory.get_user_context(user_id)
        
        self.assertIn('first_interaction', context)
        self.assertIn('last_interaction', context)
        self.assertEqual(context['total_messages'], 1)

class TestAIProcessor(unittest.TestCase):
    """Test AI processor functionality."""
    
    def setUp(self):
        """Set up test environment."""
        # Mock the Gemini API to avoid actual API calls during tests
        self.ai_processor = AIProcessor()
        self.ai_processor.model = Mock()
    
    async def test_intent_analysis_parsing(self):
        """Test intent analysis response parsing."""
        mock_response = """Intent: question
Urgency: medium
Human Support: no
Topics: technical, support
Sentiment: neutral"""
        
        mock_ai_response = Mock()
        mock_ai_response.text = mock_response
        self.ai_processor.model.generate_content = Mock(return_value=mock_ai_response)
        
        intent_data = await self.ai_processor.analyze_intent("How do I reset my password?")
        
        self.assertEqual(intent_data['intent'], 'question')
        self.assertEqual(intent_data['urgency'], 'medium')
        self.assertFalse(intent_data['requires_human'])
        self.assertIn('technical', intent_data['topics'])
        self.assertEqual(intent_data['sentiment'], 'neutral')
    
    async def test_escalation_detection(self):
        """Test escalation detection logic."""
        # Test high urgency + negative sentiment
        intent_data = {
            'urgency': 'high',
            'sentiment': 'negative',
            'requires_human': False
        }
        
        should_escalate = await self.ai_processor.should_escalate_to_human(
            intent_data, "I'm very angry about this billing issue!"
        )
        self.assertTrue(should_escalate)
        
        # Test billing keyword
        intent_data = {
            'urgency': 'low',
            'sentiment': 'neutral',
            'requires_human': False
        }
        
        should_escalate = await self.ai_processor.should_escalate_to_human(
            intent_data, "I need a refund for my purchase"
        )
        self.assertTrue(should_escalate)
    
    async def test_fallback_response(self):
        """Test fallback response when AI fails."""
        # Mock AI failure
        self.ai_processor.model.generate_content = Mock(side_effect=Exception("API Error"))
        
        response = await self.ai_processor.generate_response("Hello")
        
        self.assertIn("technical difficulties", response.lower())
        self.assertIn(Config.SUPPORT_CONTACT, response)

class TestConfiguration(unittest.TestCase):
    """Test configuration management."""
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Should pass with test environment variables
        try:
            Config.validate()
        except ValueError:
            self.fail("Config validation failed with valid environment")
    
    def test_missing_token_validation(self):
        """Test validation with missing token."""
        original_token = os.environ.get('TELEGRAM_BOT_TOKEN')

        try:
            if 'TELEGRAM_BOT_TOKEN' in os.environ:
                del os.environ['TELEGRAM_BOT_TOKEN']

            # Reload config to pick up the change
            import importlib
            import config
            importlib.reload(config)

            with self.assertRaises(ValueError):
                config.Config.validate()
        finally:
            if original_token:
                os.environ['TELEGRAM_BOT_TOKEN'] = original_token
                # Reload config again to restore
                importlib.reload(config)

async def run_async_tests():
    """Run async test methods."""
    print("Running async tests...")
    
    # Test ConversationMemory
    memory_test = TestConversationMemory()
    memory_test.setUp()
    
    try:
        await memory_test.test_add_and_get_message()
        print("✓ Conversation memory: add and get message")
        
        await memory_test.test_conversation_persistence()
        print("✓ Conversation memory: persistence")
        
        await memory_test.test_conversation_limit()
        print("✓ Conversation memory: history limit")
        
        await memory_test.test_user_context()
        print("✓ Conversation memory: user context")
        
    finally:
        memory_test.tearDown()
    
    # Test AIProcessor
    ai_test = TestAIProcessor()
    ai_test.setUp()
    
    await ai_test.test_intent_analysis_parsing()
    print("✓ AI Processor: intent analysis parsing")
    
    await ai_test.test_escalation_detection()
    print("✓ AI Processor: escalation detection")
    
    await ai_test.test_fallback_response()
    print("✓ AI Processor: fallback response")

def run_sync_tests():
    """Run synchronous test methods."""
    print("Running sync tests...")
    
    # Test Configuration
    config_test = TestConfiguration()
    
    config_test.test_config_validation()
    print("✓ Configuration: validation")
    
    config_test.test_missing_token_validation()
    print("✓ Configuration: missing token validation")

def test_file_structure():
    """Test that all required files exist."""
    print("Testing file structure...")
    
    required_files = [
        'requirements.txt',
        'config.py',
        'conversation_memory.py',
        'ai_processor.py',
        'telegram_bot.py',
        'run_bot.py',
        '.env.example',
        'README.md',
        'DEPLOYMENT.md',
        'Dockerfile',
        'docker-compose.yml'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing")
            return False
    
    return True

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        import config
        print("✓ config module imports successfully")
        
        import conversation_memory
        print("✓ conversation_memory module imports successfully")
        
        import ai_processor
        print("✓ ai_processor module imports successfully")
        
        import telegram_bot
        print("✓ telegram_bot module imports successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

async def main():
    """Main test function."""
    print("=" * 50)
    print("Telegram Customer Support Bot - Test Suite")
    print("=" * 50)
    
    # Test file structure
    if not test_file_structure():
        print("\n❌ File structure test failed")
        return False
    
    print("\n" + "=" * 30)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed")
        return False
    
    print("\n" + "=" * 30)
    
    # Run sync tests
    try:
        run_sync_tests()
    except Exception as e:
        print(f"\n❌ Sync tests failed: {e}")
        return False
    
    print("\n" + "=" * 30)
    
    # Run async tests
    try:
        await run_async_tests()
    except Exception as e:
        print(f"\n❌ Async tests failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All tests passed! Bot is ready for deployment.")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    # Set up logging for tests
    logging.basicConfig(level=logging.ERROR)
    
    # Run tests
    success = asyncio.run(main())
    
    if not success:
        exit(1)
