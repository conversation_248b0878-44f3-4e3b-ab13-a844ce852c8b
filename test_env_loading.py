#!/usr/bin/env python3
"""
Test environment loading specifically.
"""
import os
from dotenv import load_dotenv

print("=== Environment Loading Test ===")

# Test 1: Check current environment
print(f"1. Current GEMINI_API_KEY: {repr(os.getenv('GEMINI_API_KEY'))}")

# Test 2: Load .env file
print("2. Loading .env file...")
load_dotenv(override=True)
print(f"   After load_dotenv: {repr(os.getenv('GEMINI_API_KEY'))}")

# Test 3: Check file content directly
print("3. Reading .env file directly:")
try:
    with open('.env', 'r') as f:
        content = f.read()
        for line in content.split('\n'):
            if 'GEMINI_API_KEY' in line:
                print(f"   File contains: {repr(line)}")
except Exception as e:
    print(f"   Error reading file: {e}")

# Test 4: Force set and test
print("4. Force setting environment variable:")
os.environ['GEMINI_API_KEY'] = 'AIzaSyCX-cSdOfrBJop3Dlkw3Cq6TylonHjafhI'
print(f"   After manual set: {repr(os.getenv('GEMINI_API_KEY'))}")

# Test 5: Test with config module
print("5. Testing config module:")
try:
    from config import Config
    print(f"   Config.GEMINI_API_KEY: {repr(Config.GEMINI_API_KEY)}")
except Exception as e:
    print(f"   Error loading config: {e}")
