#!/usr/bin/env python3
"""
Test Gemini API key specifically.
"""
import os
import google.generativeai as genai

# Load environment
from dotenv import load_dotenv
load_dotenv(override=True)  # Force reload

def test_gemini_key():
    """Test the Gemini API key."""
    api_key = os.getenv('GEMINI_API_KEY')
    print(f"API Key: {api_key[:10]}...{api_key[-5:] if len(api_key) > 15 else api_key}")
    
    try:
        # Configure Gemini
        genai.configure(api_key=api_key)
        
        # Test with a simple model
        model = genai.GenerativeModel('gemini-pro')
        
        # Try a simple generation
        response = model.generate_content("Say hello")
        print(f"✓ Gemini API working: {response.text}")
        return True
        
    except Exception as e:
        print(f"✗ Gemini API failed: {e}")
        return False

if __name__ == "__main__":
    test_gemini_key()
