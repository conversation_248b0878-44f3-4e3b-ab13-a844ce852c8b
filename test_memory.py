#!/usr/bin/env python3
"""
Test conversation memory specifically.
"""
import asyncio
import os

async def test_memory():
    """Test conversation memory."""
    try:
        from conversation_memory import ConversationMemory
        
        print("Creating memory instance...")
        memory = ConversationMemory()
        
        print("Loading conversations...")
        await memory.load_conversations()
        
        print("Adding test message...")
        await memory.add_message("test_user", "user", "Hello")
        
        print("Getting conversation history...")
        history = await memory.get_conversation_history("test_user")
        
        print(f"✓ Memory test passed: {len(history)} messages")
        return True
        
    except Exception as e:
        print(f"✗ Memory test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_memory())
