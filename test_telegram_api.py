#!/usr/bin/env python3
"""
Test script to isolate the Telegram API issue.
"""
import os
from telegram.ext import Application

# Set test environment
os.environ['TELEGRAM_BOT_TOKEN'] = '**********************************************'
os.environ['GEMINI_API_KEY'] = 'test_key'

def test_basic_application():
    """Test basic Application creation."""
    try:
        print("Testing basic Application creation...")
        app = Application.builder().token(os.environ['TELEGRAM_BOT_TOKEN']).build()
        print("✓ Application created successfully")
        return True
    except Exception as e:
        print(f"✗ Application creation failed: {e}")
        return False

def test_with_handlers():
    """Test Application with handlers."""
    try:
        print("Testing Application with handlers...")
        from telegram.ext import CommandHandler, MessageHandler, filters
        
        async def dummy_handler(update, context):
            pass
        
        app = Application.builder().token(os.environ['TELEGRAM_BOT_TOKEN']).build()
        app.add_handler(CommandHandler("start", dummy_handler))
        app.add_handler(MessageHandler(filters.TEXT, dummy_handler))
        print("✓ Application with handlers created successfully")
        return True
    except Exception as e:
        print(f"✗ Application with handlers failed: {e}")
        return False

def test_run_polling():
    """Test run_polling method."""
    try:
        print("Testing run_polling (will timeout after 5 seconds)...")
        from telegram.ext import Application
        import signal
        import time
        
        app = Application.builder().token(os.environ['TELEGRAM_BOT_TOKEN']).build()
        
        # Set up a timeout
        def timeout_handler(signum, frame):
            raise TimeoutError("Test timeout")
        
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(5)  # 5 second timeout
        
        try:
            app.run_polling(drop_pending_updates=True)
        except TimeoutError:
            print("✓ run_polling started successfully (timed out as expected)")
            return True
        except KeyboardInterrupt:
            print("✓ run_polling started successfully (interrupted)")
            return True
        finally:
            signal.alarm(0)  # Cancel the alarm
            
    except Exception as e:
        print(f"✗ run_polling failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Telegram API Test")
    print("=" * 50)
    
    tests = [
        test_basic_application,
        test_with_handlers,
        test_run_polling
    ]
    
    for test in tests:
        try:
            if not test():
                print(f"\n❌ Test failed: {test.__name__}")
                break
            print()
        except Exception as e:
            print(f"\n❌ Test crashed: {test.__name__} - {e}")
            break
    else:
        print("🎉 All tests passed!")
