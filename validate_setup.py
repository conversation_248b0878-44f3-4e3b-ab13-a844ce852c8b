#!/usr/bin/env python3
"""
Simple validation script for the Telegram Customer Support Bot setup.
"""
import os
import sys

def check_files():
    """Check that all required files exist."""
    print("Checking file structure...")
    
    required_files = [
        'requirements.txt',
        'config.py',
        'conversation_memory.py',
        'ai_processor.py',
        'telegram_bot.py',
        'run_bot.py',
        '.env.example',
        'README.md',
        'DEPLOYMENT.md',
        'Dockerfile',
        'docker-compose.yml'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - MISSING")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def check_imports():
    """Check that all modules can be imported."""
    print("\nChecking imports...")
    
    # Set test environment variables to avoid validation errors
    os.environ['TELEGRAM_BOT_TOKEN'] = 'test_token'
    os.environ['GEMINI_API_KEY'] = 'test_gemini_key'
    
    modules = [
        'config',
        'conversation_memory',
        'ai_processor',
        'telegram_bot'
    ]
    
    failed_imports = []
    for module in modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module} - IMPORT ERROR: {e}")
            failed_imports.append(module)
        except Exception as e:
            print(f"✗ {module} - ERROR: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def check_configuration():
    """Check configuration validation."""
    print("\nChecking configuration...")
    
    try:
        from config import Config
        Config.validate()
        print("✓ Configuration validation passed")
        return True
    except Exception as e:
        print(f"✗ Configuration validation failed: {e}")
        return False

def check_dependencies():
    """Check that all dependencies are installed."""
    print("\nChecking dependencies...")
    
    dependencies = [
        'telegram',
        'google.generativeai',
        'aiofiles',
        'asyncio_throttle',
        'dotenv'
    ]
    
    missing_deps = []
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep}")
        except ImportError:
            print(f"✗ {dep} - NOT INSTALLED")
            missing_deps.append(dep)
    
    return len(missing_deps) == 0

def main():
    """Main validation function."""
    print("=" * 60)
    print("Telegram Customer Support Bot - Setup Validation")
    print("=" * 60)
    
    all_checks_passed = True
    
    # Check files
    if not check_files():
        all_checks_passed = False
    
    # Check dependencies
    if not check_dependencies():
        all_checks_passed = False
        print("\nTo install dependencies, run:")
        print("pip3 install -r requirements.txt")
    
    # Check imports
    if not check_imports():
        all_checks_passed = False
    
    # Check configuration
    if not check_configuration():
        all_checks_passed = False
    
    print("\n" + "=" * 60)
    
    if all_checks_passed:
        print("🎉 All validation checks passed!")
        print("\nNext steps:")
        print("1. Copy .env.example to .env")
        print("2. Add your real API keys to .env")
        print("3. Run: python3 run_bot.py")
        print("\nFor deployment instructions, see DEPLOYMENT.md")
    else:
        print("❌ Some validation checks failed.")
        print("Please fix the issues above before proceeding.")
        return False
    
    print("=" * 60)
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
